# 做市商对冲对账流程文档

## 概述

本文档详细描述了做市商对冲对账的完整流程，包括完全合约对冲和部分合约对冲两种模式的对比说明，以及相关的字段定义和计算公式。

## 对冲模式对比表

| 对账形式 | 完全合约对冲 | 部分合约对冲 |
|---------|-------------|-------------|
| **适用场景** | 初始净头寸为0的用户 | 存在初始净头寸的用户 |
| **对冲策略** | 对所有开平仓进行1:1对冲 | 仅对净头寸变化部分进行对冲 |
| **初始状态** | 合约资产净值记录，净头寸=0 | 合约资产净值记录，净头寸≠0 |
| **特殊处理** | 无 | 忽略初始净头寸的平仓行为 |

## 记录字段定义

### 基础交易字段

| 字段名称 | 数据类型 | 说明 | 示例 |
|---------|---------|------|------|
| **开仓时间** | DateTime | 开仓交易执行时间（UTC+8） | 2025-08-02 10:30:00 |
| **开仓均价** | Decimal | 开仓成交的平均价格 | 65000.50 USDT |
| **开仓手续费** | Decimal | 开仓产生的手续费 | 19.50 USDT |
| **开仓数量** | Decimal | 开仓的合约数量 | 1.0 手 |
| **开仓方向** | Integer | 开仓方向（1=买入，-1=卖出） | 1 |
| **平仓时间** | DateTime | 平仓交易执行时间（UTC+8） | 2025-08-03 10:30:00 |
| **平仓均价** | Decimal | 平仓成交的平均价格 | 66000.00 USDT |
| **平仓数量** | Decimal | 平仓的合约数量 | 1.0 手 |
| **平仓手续费** | Decimal | 平仓产生的手续费 | 19.80 USDT |
| **资金费用** | Decimal | 持仓期间产生的资金费用 | -5.20 USDT |

### 计算字段

| 字段名称 | 计算公式 | 说明 |
|---------|---------|------|
| **交易盈亏** | (平仓均价 - 开仓均价) × 开仓方向 × 平仓数量 | 不含手续费的纯价差盈亏 |
| **净盈亏** | 交易盈亏 - 开仓手续费 - 平仓手续费 - 资金费用 | 包含所有成本的最终盈亏 |

## 盈亏计算公式

### 标准计算公式

```
期初合约净值 - 期末合约净值 = 净盈亏

净盈亏 = 交易盈亏 - 开仓手续费 - 平仓手续费 - 资金费用

其中：
交易盈亏 = (平仓均价 - 开仓均价) × 开仓方向 × 平仓数量
开仓手续费 = 开仓均价 × 开仓数量 × 交易费率
平仓手续费 = 平仓均价 × 平仓数量 × 交易费率
资金费用 = 标记价格 × 持仓数量 × 资金费率 × 持仓天数

注：开仓方向：买入=1，卖出=-1
```

## 具体案例分析

### 案例1：完全合约对冲（uid1）

**基本信息**
- 用户ID：uid1
- 对冲开始时间：2025-08-01 00:00:00 (UTC+8)
- 初始状态：合约资产净值记录，所有净头寸为0
- 交易平台：bitda
- 对冲平台：binance

**交易详情**
- 交易时间：2025-08-02 10:30:00 (UTC+8)
- 交易品种：BTCUSDT永续合约
- 交易方向：买入
- 交易数量：1手
- 开仓均价：65000.50 USDT
- 持仓时间：1天
- 平仓时间：2025-08-03 10:30:00 (UTC+8)
- 平仓均价：66000.00 USDT

**费用计算**
- 交易费率：0.0003 (0.03%)
- 开仓手续费：65000.50 × 1 × 0.0003 = 19.50 USDT
- 平仓手续费：66000.00 × 1 × 0.0003 = 19.80 USDT
- 资金费率：-0.0001 (每8小时)
- 资金费用：65500 × 1 × (-0.0001) × 3 = -19.65 USDT

**盈亏计算**
```
交易盈亏 = (66000.00 - 65000.50) × 1 × 1 = 999.50 USDT
净盈亏 = 999.50 - 19.50 - 19.80 - (-19.65) = 979.85 USDT
```

### 案例2：部分合约对冲（uid2）

**基本信息**
- 用户ID：uid2
- 对冲开始时间：2025-08-03 00:00:00 (UTC+8)
- 初始状态：合约资产净值记录，净头寸为+1 ETH
- 特殊规则：忽略+1 ETH净头寸的平仓行为

**交易详情**
- 交易时间：2025-08-03 14:20:00 (UTC+8)
- 交易品种：ETHUSDT永续合约
- 交易方向：卖出
- 交易数量：1手
- 开仓均价：3200.00 USDT
- 持仓时间：1天
- 平仓时间：2025-08-04 14:20:00 (UTC+8)
- 平仓均价：3150.00 USDT

**对冲逻辑**
由于用户初始净头寸为+1 ETH，当用户卖出1手时：
- 实际净头寸变化：从+1变为0
- 做市商对冲：在binance卖出1手ETHUSDT永续合约

**盈亏计算**
```
交易盈亏 = (3150.00 - 3200.00) × (-1) × 1 = 50.00 USDT
开仓手续费 = 3200.00 × 1 × 0.0003 = 0.96 USDT
平仓手续费 = 3150.00 × 1 × 0.0003 = 0.945 USDT
资金费用 = 3175 × 1 × 0.0001 × 3 = 0.9525 USDT
净盈亏 = 50.00 - 0.96 - 0.945 - 0.9525 = 47.1425 USDT
```

### 案例3：特殊对冲情况（uid3）

**基本信息**
- 用户ID：uid3
- 对冲开始时间：2025-08-04 00:00:00 (UTC+8)
- 初始状态：净头寸为+1 ETH
- 特殊处理：该+1 ETH头寸作为独立结算，不参与对冲盈亏计算

**处理方式**
1. 将初始+1 ETH头寸单独记录和结算
2. 对后续交易按照标准流程进行对冲
3. 盈亏计算时排除初始头寸的影响

## 异常情况处理

### 对冲失败处理

| 异常类型 | 处理方式 | 记录要求 |
|---------|---------|---------|
| **网络延迟** | 重试机制，最多3次 | 记录重试次数和时间差 |
| **流动性不足** | 分批对冲或市价对冲 | 记录实际成交价格和数量 |
| **系统故障** | 手动对冲并标记 | 详细记录故障原因和处理时间 |

### 数据一致性检查

1. **实时检查**：每笔交易后立即验证对冲状态
2. **定期检查**：每小时进行全量数据对比
3. **日终检查**：每日结束后进行完整对账

## 报表输出格式

### 日度对账报表

| 用户ID | 交易品种 | 开仓时间 | 平仓时间 | 交易盈亏 | 手续费 | 资金费用 | 净盈亏 | 对冲状态 |
|-------|---------|---------|---------|---------|-------|---------|-------|---------|
| uid1 | BTCUSDT | 08-02 10:30 | 08-03 10:30 | 999.50 | 39.30 | -19.65 | 979.85 | 已对冲 |
| uid2 | ETHUSDT | 08-03 14:20 | 08-04 14:20 | 50.00 | 1.905 | 0.9525 | 47.14 | 已对冲 |

### 异常报表

| 时间 | 用户ID | 异常类型 | 异常描述 | 处理状态 | 处理人员 |
|------|-------|---------|---------|---------|---------|
| 08-02 10:31 | uid1 | 网络延迟 | 对冲延迟30秒 | 已处理 | 系统自动 |

## 技术实现要点

1. **时区统一**：所有时间戳统一使用UTC+8时区
2. **精度控制**：价格精度保持8位小数，数量精度保持6位小数
3. **实时监控**：建立实时监控机制，及时发现异常
4. **数据备份**：重要数据实时备份，确保数据安全

## 流程图（Mermaid格式）

```mermaid
flowchart TD
    A[开始对冲对账] --> B[检查用户初始状态]
    B --> C{初始净头寸是否为0？}
    C -->|是| D[完全合约对冲模式]
    C -->|否| E[部分合约对冲模式]
    D --> F[监听用户交易]
    E --> F
    F --> G{是否有新交易？}
    G -->|否| F
    G -->|是| H[执行对冲交易]
    H --> I{对冲是否成功？}
    I -->|是| J[记录交易数据]
    I -->|否| K[异常处理]
    K --> L{是否需要重试？}
    L -->|是| H
    L -->|否| M[手动处理并标记]
    J --> N[计算盈亏]
    N --> O[生成对账报表]
    M --> O
    O --> P[对账完成]

    style A fill:#d5e8d4
    style P fill:#d5e8d4
    style D fill:#e1d5e7
    style E fill:#f8cecc
    style K fill:#f8cecc
    style M fill:#f8cecc
```

---

*文档版本：v1.0*
*最后更新：2025-08-04*
*维护人员：系统管理员*
