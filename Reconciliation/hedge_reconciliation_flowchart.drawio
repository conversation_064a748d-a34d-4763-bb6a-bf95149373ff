<mxfile host="app.diagrams.net" modified="2025-08-04T00:00:00.000Z" agent="5.0" etag="xxx" version="24.6.4" type="device">
  <diagram name="对冲对账流程图" id="hedge-reconciliation-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 开始节点 -->
        <mxCell id="start" value="开始对冲对账" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 初始化检查 -->
        <mxCell id="init-check" value="检查用户初始状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="500" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 判断净头寸 -->
        <mxCell id="position-check" value="初始净头寸是否为0？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="480" y="240" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 完全对冲分支 -->
        <mxCell id="full-hedge" value="完全合约对冲模式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="280" y="380" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 部分对冲分支 -->
        <mxCell id="partial-hedge" value="部分合约对冲模式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="720" y="380" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 交易监听 -->
        <mxCell id="trade-monitor" value="监听用户交易" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="500" y="500" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 交易判断 -->
        <mxCell id="trade-check" value="是否有新交易？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="480" y="600" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 对冲执行 -->
        <mxCell id="hedge-execute" value="执行对冲交易" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="280" y="720" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 对冲成功判断 -->
        <mxCell id="hedge-success" value="对冲是否成功？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="480" y="720" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 异常处理 -->
        <mxCell id="exception-handle" value="异常处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="720" y="720" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 重试判断 -->
        <mxCell id="retry-check" value="是否需要重试？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="720" y="840" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 记录交易 -->
        <mxCell id="record-trade" value="记录交易数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="280" y="840" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 计算盈亏 -->
        <mxCell id="calculate-pnl" value="计算盈亏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="280" y="960" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 生成报表 -->
        <mxCell id="generate-report" value="生成对账报表" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="500" y="960" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 手动处理 -->
        <mxCell id="manual-handle" value="手动处理并标记" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="960" y="840" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 结束节点 -->
        <mxCell id="end" value="对账完成" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="edge1" edge="1" parent="1" source="start" target="init-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="init-check" target="position-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="position-check" target="full-hedge">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge3-label" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge3">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="position-check" target="partial-hedge">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge4-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge4">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="full-hedge" target="trade-monitor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="partial-hedge" target="trade-monitor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="trade-monitor" target="trade-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="trade-check" target="hedge-execute">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge8-label" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge8">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="hedge-execute" target="hedge-success">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="hedge-success" target="exception-handle">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge10-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge10">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge11" edge="1" parent="1" source="hedge-success" target="record-trade">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge11-label" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge11">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge12" edge="1" parent="1" source="exception-handle" target="retry-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge13" edge="1" parent="1" source="retry-check" target="hedge-execute">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="820" y="960" />
              <mxPoint x="200" y="960" />
              <mxPoint x="200" y="750" />
            </Array>
          </mxGeometry>
          <mxCell id="edge13-label" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge13">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge14" edge="1" parent="1" source="retry-check" target="manual-handle">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge14-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge14">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge15" edge="1" parent="1" source="record-trade" target="calculate-pnl">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge16" edge="1" parent="1" source="calculate-pnl" target="generate-report">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge17" edge="1" parent="1" source="generate-report" target="end">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge18" edge="1" parent="1" source="manual-handle" target="generate-report">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1040" y="990" />
              <mxPoint x="580" y="990" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge19" edge="1" parent="1" source="trade-check" target="trade-monitor">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="680" y="640" />
              <mxPoint x="760" y="640" />
              <mxPoint x="760" y="530" />
            </Array>
          </mxGeometry>
          <mxCell id="edge19-label" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge19">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <!-- 说明文本框 -->
        <mxCell id="legend" value="流程说明：&#xa;1. 绿色：开始/结束节点&#xa;2. 黄色：数据处理节点&#xa;3. 橙色：判断节点&#xa;4. 紫色：对冲模式节点&#xa;5. 红色：异常处理节点&#xa;6. 蓝色：监控和报表节点" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="200" height="140" as="geometry" />
        </mxCell>
        
        <!-- 特殊处理说明 -->
        <mxCell id="special-note" value="特殊情况处理：&#xa;• 网络延迟：最多重试3次&#xa;• 流动性不足：分批或市价对冲&#xa;• 系统故障：手动处理并标记&#xa;• 数据异常：记录详细日志" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="960" y="40" width="200" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
