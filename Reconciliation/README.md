# 对冲对账文档说明

## 文档概述

本目录包含做市商对冲对账系统的完整文档，包括详细的流程说明、计算公式和可视化流程图。

## 文件说明

### 1. hedge_reconciliation_process.md
**对账流程文档**
- 📋 详细的对账流程说明
- 📊 完全合约对冲 vs 部分合约对冲对比表
- 🔢 字段定义和计算公式
- 📈 三个具体案例分析
- 🚨 异常情况处理方案
- 📑 报表格式示例

### 2. hedge_reconciliation_flowchart.drawio
**Draw.io流程图文件**
- 🎨 可视化对冲对账流程
- 🔄 决策分支和数据流向
- ⚠️ 异常处理路径
- 📝 详细的流程节点说明

## 快速开始

### 查看流程文档
```bash
# 在Markdown编辑器中打开
cat hedge_reconciliation_process.md
```

### 编辑流程图
1. 访问 [draw.io](https://app.diagrams.net/)
2. 选择 "Open Existing Diagram"
3. 上传 `hedge_reconciliation_flowchart.drawio` 文件
4. 进行编辑和修改

### 在线预览Mermaid流程图
流程文档中包含Mermaid格式的流程图，可以在支持Mermaid的Markdown编辑器中直接预览。

## 核心概念

### 对冲模式
- **完全合约对冲**：适用于初始净头寸为0的用户
- **部分合约对冲**：适用于存在初始净头寸的用户

### 关键字段
- 开仓/平仓时间、均价、数量、手续费
- 资金费用、交易盈亏、净盈亏
- 对冲状态、异常记录

### 计算公式
```
净盈亏 = 交易盈亏 - 开仓手续费 - 平仓手续费 - 资金费用
交易盈亏 = (平仓均价 - 开仓均价) × 开仓方向 × 平仓数量
```

## 案例说明

### 案例1：完全合约对冲（uid1）
- 初始净头寸：0
- 交易：BTCUSDT买入1手
- 对冲：binance同步买入1手
- 盈亏：979.85 USDT

### 案例2：部分合约对冲（uid2）
- 初始净头寸：+1 ETH
- 交易：ETHUSDT卖出1手
- 对冲：binance同步卖出1手
- 盈亏：47.14 USDT

### 案例3：特殊对冲情况（uid3）
- 初始净头寸：+1 ETH（独立结算）
- 特殊处理：排除初始头寸影响

## 异常处理

| 异常类型 | 处理方式 | 最大重试次数 |
|---------|---------|-------------|
| 网络延迟 | 自动重试 | 3次 |
| 流动性不足 | 分批/市价对冲 | - |
| 系统故障 | 手动处理 | - |

## 技术要求

- **时区**：统一使用UTC+8
- **精度**：价格8位小数，数量6位小数
- **监控**：实时监控和异常告警
- **备份**：重要数据实时备份

## 更新日志

- **v1.0** (2025-08-04)
  - 初始版本发布
  - 包含完整的对账流程文档
  - 添加可视化流程图
  - 提供三个详细案例

## 联系方式

如有问题或建议，请联系：
- 系统管理员
- 技术支持团队

---

*最后更新：2025-08-04*
