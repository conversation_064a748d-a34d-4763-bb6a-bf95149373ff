#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对冲对账Excel模板生成器
基于hedge_reconciliation_process.md文档中的案例数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def create_hedge_reconciliation_excel():
    """创建对冲对账Excel模板"""
    
    # 创建工作簿
    wb = Workbook()
    
    # 删除默认工作表
    wb.remove(wb.active)
    
    # 定义样式
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center')
    
    # 案例数据定义
    case1_data = {
        'uid': 'uid1',
        'symbol': 'BTCUSDT',
        'open_time': '2025-08-04 14:30:00',
        'close_time': '2025-08-05 14:30:00',
        'direction': 'BUY',
        'quantity': 0.5,
        'open_price_bitda': 110000.00,
        'close_price_bitda': 112000.00,
        'open_price_binance': 109950.00,
        'close_price_binance': 111920.00,
        'bitda_fee_rate': 0.0006,
        'binance_fee_rate': 0.0005,
        'funding_rate': 0.0001
    }
    
    case2_data = {
        'uid': 'uid2',
        'symbol': 'ETHUSDT',
        'open_time': '2025-08-04 16:15:00',
        'close_time': '2025-08-05 04:15:00',
        'direction': 'SELL',
        'quantity': 1.0,
        'open_price_bitda': 3500.00,
        'close_price_bitda': 3450.00,
        'open_price_binance': 3485.00,
        'close_price_binance': 3435.00,
        'bitda_fee_rate': 0.0006,
        'binance_fee_rate': 0.0005,
        'funding_rate': 0.0001
    }
    
    # 1. Bitda汇总页
    ws_bitda_summary = wb.create_sheet("Bitda汇总页")
    create_bitda_summary_sheet(ws_bitda_summary, case1_data, case2_data, header_font, header_fill, border)
    
    # 2. Bitda_uid1交易页
    ws_bitda_uid1 = wb.create_sheet("Bitda_uid1交易页")
    create_bitda_uid1_sheet(ws_bitda_uid1, case1_data, header_font, header_fill, border)
    
    # 3. Bitda_uid2交易页
    ws_bitda_uid2 = wb.create_sheet("Bitda_uid2交易页")
    create_bitda_uid2_sheet(ws_bitda_uid2, case2_data, header_font, header_fill, border)
    
    # 4. Binance交易页
    ws_binance = wb.create_sheet("Binance交易页")
    create_binance_sheet(ws_binance, case1_data, case2_data, header_font, header_fill, border)
    
    # 5. Binance汇总页
    ws_binance_summary = wb.create_sheet("Binance汇总页")
    create_binance_summary_sheet(ws_binance_summary, case1_data, case2_data, header_font, header_fill, border)
    
    # 6. 对账汇总页
    ws_reconciliation = wb.create_sheet("对账汇总页")
    create_reconciliation_sheet(ws_reconciliation, case1_data, case2_data, header_font, header_fill, border)
    
    # 保存文件
    output_path = "Reconciliation/hedge_reconciliation_template.xlsx"
    wb.save(output_path)
    print(f"Excel模板已创建: {output_path}")
    return output_path

def create_bitda_summary_sheet(ws, case1, case2, header_font, header_fill, border):
    """创建Bitda汇总页"""
    ws.title = "Bitda汇总页"
    
    # 标题
    ws['A1'] = "Bitda平台交易汇总"
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:P1')
    
    # 表头
    headers = [
        "用户ID", "交易品种", "交易方向", "开仓时间", "平仓时间", 
        "交易数量", "开仓价格", "平仓价格", "交易金额", "交易盈亏",
        "开仓手续费", "平仓手续费", "资金费用", "净盈亏", "备注"
    ]
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')
    
    # 案例1数据
    case1_row = [
        case1['uid'],
        case1['symbol'],
        case1['direction'],
        case1['open_time'],
        case1['close_time'],
        case1['quantity'],
        case1['open_price_bitda'],
        case1['close_price_bitda'],
        case1['quantity'] * case1['open_price_bitda'],  # 交易金额
        (case1['close_price_bitda'] - case1['open_price_bitda']) * case1['quantity'],  # 交易盈亏
        case1['open_price_bitda'] * case1['quantity'] * case1['bitda_fee_rate'],  # 开仓手续费
        case1['close_price_bitda'] * case1['quantity'] * case1['bitda_fee_rate'],  # 平仓手续费
        16.50,  # 资金费用（从文档中获取）
        949.90,  # 净盈亏（从文档中获取）
        "完全对冲模式"
    ]
    
    # 案例2数据
    case2_row = [
        case2['uid'],
        case2['symbol'],
        case2['direction'],
        case2['open_time'],
        case2['close_time'],
        case2['quantity'],
        case2['open_price_bitda'],
        case2['close_price_bitda'],
        case2['quantity'] * case2['open_price_bitda'],  # 交易金额
        (case2['close_price_bitda'] - case2['open_price_bitda']) * case2['quantity'] * (-1),  # 交易盈亏（卖出）
        case2['open_price_bitda'] * case2['quantity'] * case2['bitda_fee_rate'],  # 开仓手续费
        case2['close_price_bitda'] * case2['quantity'] * case2['bitda_fee_rate'],  # 平仓手续费
        -1.05,  # 资金费用（从文档中获取）
        44.78,  # 净盈亏（从文档中获取）
        "部分对冲模式"
    ]
    
    # 填充数据
    for col, value in enumerate(case1_row, 1):
        cell = ws.cell(row=4, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 5:
            cell.number_format = '#,##0.00'
    
    for col, value in enumerate(case2_row, 1):
        cell = ws.cell(row=5, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 5:
            cell.number_format = '#,##0.00'
    
    # 汇总行
    ws.cell(row=7, column=1, value="汇总").font = Font(bold=True)
    ws.cell(row=7, column=6, value="=SUM(F4:F5)")  # 总数量
    ws.cell(row=7, column=9, value="=SUM(I4:I5)")  # 总交易金额
    ws.cell(row=7, column=10, value="=SUM(J4:J5)")  # 总交易盈亏
    ws.cell(row=7, column=11, value="=SUM(K4:K5)")  # 总开仓手续费
    ws.cell(row=7, column=12, value="=SUM(L4:L5)")  # 总平仓手续费
    ws.cell(row=7, column=13, value="=SUM(M4:M5)")  # 总资金费用
    ws.cell(row=7, column=14, value="=SUM(N4:N5)")  # 总净盈亏
    
    # 设置列宽
    column_widths = [12, 12, 12, 20, 20, 12, 15, 15, 15, 15, 15, 15, 15, 15, 15]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

def create_bitda_uid1_sheet(ws, case1, header_font, header_fill, border):
    """创建Bitda_uid1交易页"""
    ws.title = "Bitda_uid1交易页"
    
    # 标题
    ws['A1'] = f"Bitda平台 - {case1['uid']} 详细交易记录"
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:P1')
    
    # 表头
    headers = [
        "time", "uid", "user_id", "order_id", "position_id", "market", 
        "持仓模式", "仓位方向", "成交类型", "杠杆", "成交价格", "成交数量", 
        "成交金额", "成交盈亏", "手续费", "强平罚没", "资金费用"
    ]
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')
    
    # 开仓记录
    open_row = [
        case1['open_time'],
        case1['uid'],
        case1['uid'],
        "ORD_001_OPEN",
        "POS_001",
        case1['symbol'],
        "逐仓",
        "多头" if case1['direction'] == 'BUY' else "空头",
        "开仓",
        "1x",
        case1['open_price_bitda'],
        case1['quantity'],
        case1['quantity'] * case1['open_price_bitda'],
        0,  # 开仓时盈亏为0
        case1['open_price_bitda'] * case1['quantity'] * case1['bitda_fee_rate'],
        0,  # 无强平罚没
        0   # 开仓时无资金费用
    ]
    
    # 平仓记录
    close_row = [
        case1['close_time'],
        case1['uid'],
        case1['uid'],
        "ORD_001_CLOSE",
        "POS_001",
        case1['symbol'],
        "逐仓",
        "多头" if case1['direction'] == 'BUY' else "空头",
        "平仓",
        "1x",
        case1['close_price_bitda'],
        case1['quantity'],
        case1['quantity'] * case1['close_price_bitda'],
        (case1['close_price_bitda'] - case1['open_price_bitda']) * case1['quantity'],
        case1['close_price_bitda'] * case1['quantity'] * case1['bitda_fee_rate'],
        0,  # 无强平罚没
        16.50  # 资金费用（从文档获取）
    ]
    
    # 填充数据
    for col, value in enumerate(open_row, 1):
        cell = ws.cell(row=4, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 10:
            cell.number_format = '#,##0.00'
    
    for col, value in enumerate(close_row, 1):
        cell = ws.cell(row=5, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 10:
            cell.number_format = '#,##0.00'
    
    # 设置列宽
    column_widths = [20, 8, 10, 15, 12, 12, 10, 10, 10, 8, 12, 12, 15, 15, 12, 12, 12]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

def create_bitda_uid2_sheet(ws, case2, header_font, header_fill, border):
    """创建Bitda_uid2交易页"""
    ws.title = "Bitda_uid2交易页"
    
    # 标题
    ws['A1'] = f"Bitda平台 - {case2['uid']} 详细交易记录"
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:P1')
    
    # 表头
    headers = [
        "time", "uid", "user_id", "order_id", "position_id", "market", 
        "持仓模式", "仓位方向", "成交类型", "杠杆", "成交价格", "成交数量", 
        "成交金额", "成交盈亏", "手续费", "强平罚没", "资金费用"
    ]
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')
    
    # 开仓记录
    open_row = [
        case2['open_time'],
        case2['uid'],
        case2['uid'],
        "ORD_002_OPEN",
        "POS_002",
        case2['symbol'],
        "逐仓",
        "空头" if case2['direction'] == 'SELL' else "多头",
        "开仓",
        "1x",
        case2['open_price_bitda'],
        case2['quantity'],
        case2['quantity'] * case2['open_price_bitda'],
        0,  # 开仓时盈亏为0
        case2['open_price_bitda'] * case2['quantity'] * case2['bitda_fee_rate'],
        0,  # 无强平罚没
        0   # 开仓时无资金费用
    ]
    
    # 平仓记录
    close_row = [
        case2['close_time'],
        case2['uid'],
        case2['uid'],
        "ORD_002_CLOSE",
        "POS_002",
        case2['symbol'],
        "逐仓",
        "空头" if case2['direction'] == 'SELL' else "多头",
        "平仓",
        "1x",
        case2['close_price_bitda'],
        case2['quantity'],
        case2['quantity'] * case2['close_price_bitda'],
        (case2['open_price_bitda'] - case2['close_price_bitda']) * case2['quantity'],  # 空头盈亏
        case2['close_price_bitda'] * case2['quantity'] * case2['bitda_fee_rate'],
        0,  # 无强平罚没
        -1.05  # 资金费用（从文档获取）
    ]
    
    # 填充数据
    for col, value in enumerate(open_row, 1):
        cell = ws.cell(row=4, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 10:
            cell.number_format = '#,##0.00'
    
    for col, value in enumerate(close_row, 1):
        cell = ws.cell(row=5, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 10:
            cell.number_format = '#,##0.00'
    
    # 设置列宽
    column_widths = [20, 8, 10, 15, 12, 12, 10, 10, 10, 8, 12, 12, 15, 15, 12, 12, 12]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

def create_binance_sheet(ws, case1, case2, header_font, header_fill, border):
    """创建Binance交易页"""
    ws.title = "Binance交易页"

    # 标题
    ws['A1'] = "Binance平台对冲交易记录"
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:P1')

    # 表头
    headers = [
        "time", "uid", "user_id", "order_id", "position_id", "market",
        "持仓模式", "仓位方向", "成交类型", "杠杆", "成交价格", "成交数量",
        "成交金额", "成交盈亏", "手续费", "强平罚没", "资金费用"
    ]

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')

    # 案例1对冲记录 - 开仓（实际交易记录，不包含滑点成本）
    case1_open = [
        "2025-08-04 14:30:00.150",  # 延迟150ms
        "HEDGE_" + case1['uid'],
        "HEDGE_" + case1['uid'],
        "HEDGE_ORD_001_OPEN",
        "HEDGE_POS_001",
        case1['symbol'],
        "逐仓",
        "多头" if case1['direction'] == 'BUY' else "空头",
        "开仓",
        "1x",
        case1['open_price_binance'],  # 实际成交价格（已包含滑点影响）
        case1['quantity'],
        case1['quantity'] * case1['open_price_binance'],
        0,  # 开仓时盈亏为0
        case1['open_price_binance'] * case1['quantity'] * case1['binance_fee_rate'],
        0,  # 无强平罚没
        0   # 开仓时无资金费用
    ]

    # 案例1对冲记录 - 平仓（实际交易记录，不包含滑点成本）
    case1_close = [
        "2025-08-05 14:30:00.120",  # 延迟120ms
        "HEDGE_" + case1['uid'],
        "HEDGE_" + case1['uid'],
        "HEDGE_ORD_001_CLOSE",
        "HEDGE_POS_001",
        case1['symbol'],
        "逐仓",
        "多头" if case1['direction'] == 'BUY' else "空头",
        "平仓",
        "1x",
        case1['close_price_binance'],  # 实际成交价格（已包含滑点影响）
        case1['quantity'],
        case1['quantity'] * case1['close_price_binance'],
        (case1['close_price_binance'] - case1['open_price_binance']) * case1['quantity'],
        case1['close_price_binance'] * case1['quantity'] * case1['binance_fee_rate'],
        0,  # 无强平罚没
        16.79  # 资金费用（从文档获取）
    ]

    # 案例2对冲记录 - 开仓（实际交易记录，不包含滑点成本）
    case2_open = [
        "2025-08-04 16:15:00.180",  # 延迟180ms
        "HEDGE_" + case2['uid'],
        "HEDGE_" + case2['uid'],
        "HEDGE_ORD_002_OPEN",
        "HEDGE_POS_002",
        case2['symbol'],
        "逐仓",
        "空头" if case2['direction'] == 'SELL' else "多头",
        "开仓",
        "1x",
        case2['open_price_binance'],  # 实际成交价格（已包含滑点影响）
        case2['quantity'],
        case2['quantity'] * case2['open_price_binance'],
        0,  # 开仓时盈亏为0
        case2['open_price_binance'] * case2['quantity'] * case2['binance_fee_rate'],
        0,  # 无强平罚没
        0   # 开仓时无资金费用
    ]

    # 案例2对冲记录 - 平仓（实际交易记录，不包含滑点成本）
    case2_close = [
        "2025-08-05 04:15:00.160",  # 延迟160ms
        "HEDGE_" + case2['uid'],
        "HEDGE_" + case2['uid'],
        "HEDGE_ORD_002_CLOSE",
        "HEDGE_POS_002",
        case2['symbol'],
        "逐仓",
        "空头" if case2['direction'] == 'SELL' else "多头",
        "平仓",
        "1x",
        case2['close_price_binance'],  # 实际成交价格（已包含滑点影响）
        case2['quantity'],
        case2['quantity'] * case2['close_price_binance'],
        (case2['open_price_binance'] - case2['close_price_binance']) * case2['quantity'],  # 空头盈亏
        case2['close_price_binance'] * case2['quantity'] * case2['binance_fee_rate'],
        0,  # 无强平罚没
        -1.04  # 资金费用（从文档获取）
    ]

    # 填充数据
    rows = [case1_open, case1_close, case2_open, case2_close]
    for row_idx, row_data in enumerate(rows, 4):
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col, value=value)
            cell.border = border
            if isinstance(value, (int, float)) and col > 10:
                cell.number_format = '#,##0.00'

    # 设置列宽
    column_widths = [20, 12, 12, 18, 15, 12, 10, 10, 10, 8, 12, 12, 15, 15, 12, 12, 12]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

def create_binance_summary_sheet(ws, case1, case2, header_font, header_fill, border):
    """创建Binance汇总页"""
    ws.title = "Binance汇总页"

    # 标题
    ws['A1'] = "Binance平台对冲交易汇总"
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:P1')

    # 表头（移除滑点成本，这应该在分析页面展示）
    headers = [
        "对冲用户", "交易品种", "交易方向", "开仓时间", "平仓时间",
        "交易数量", "开仓价格", "平仓价格", "交易金额", "交易盈亏",
        "开仓手续费", "平仓手续费", "资金费用", "净盈亏", "备注"
    ]

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')

    # 案例1对冲数据（纯交易数据，不包含分析指标）
    case1_row = [
        "HEDGE_" + case1['uid'],
        case1['symbol'],
        case1['direction'],
        "2025-08-04 14:30:00.150",
        "2025-08-05 14:30:00.120",
        case1['quantity'],
        case1['open_price_binance'],
        case1['close_price_binance'],
        case1['quantity'] * case1['open_price_binance'],
        (case1['close_price_binance'] - case1['open_price_binance']) * case1['quantity'],
        case1['open_price_binance'] * case1['quantity'] * case1['binance_fee_rate'],
        case1['close_price_binance'] * case1['quantity'] * case1['binance_fee_rate'],
        16.79,  # 资金费用
        816.32,  # 净盈亏（从文档获取）
        "完全对冲"  # 备注
    ]

    # 案例2对冲数据（纯交易数据，不包含分析指标）
    case2_row = [
        "HEDGE_" + case2['uid'],
        case2['symbol'],
        case2['direction'],
        "2025-08-04 16:15:00.180",
        "2025-08-05 04:15:00.160",
        case2['quantity'],
        case2['open_price_binance'],
        case2['close_price_binance'],
        case2['quantity'] * case2['open_price_binance'],
        (case2['open_price_binance'] - case2['close_price_binance']) * case2['quantity'],
        case2['open_price_binance'] * case2['quantity'] * case2['binance_fee_rate'],
        case2['close_price_binance'] * case2['quantity'] * case2['binance_fee_rate'],
        -1.04,  # 资金费用
        0.50,   # 净盈亏（从文档获取）
        "完全对冲"  # 备注
    ]

    # 填充数据
    for col, value in enumerate(case1_row, 1):
        cell = ws.cell(row=4, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 5:
            cell.number_format = '#,##0.00'

    for col, value in enumerate(case2_row, 1):
        cell = ws.cell(row=5, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 5:
            cell.number_format = '#,##0.00'

    # 汇总行（调整列号，因为移除了滑点成本列）
    ws.cell(row=7, column=1, value="汇总").font = Font(bold=True)
    ws.cell(row=7, column=6, value="=SUM(F4:F5)")  # 总数量
    ws.cell(row=7, column=9, value="=SUM(I4:I5)")  # 总交易金额
    ws.cell(row=7, column=10, value="=SUM(J4:J5)")  # 总交易盈亏
    ws.cell(row=7, column=11, value="=SUM(K4:K5)")  # 总开仓手续费
    ws.cell(row=7, column=12, value="=SUM(L4:L5)")  # 总平仓手续费
    ws.cell(row=7, column=13, value="=SUM(M4:M5)")  # 总资金费用
    ws.cell(row=7, column=14, value="=SUM(N4:N5)")  # 总净盈亏

    # 设置列宽
    column_widths = [15, 12, 12, 20, 20, 12, 15, 15, 15, 15, 15, 15, 15, 15, 15]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

def create_reconciliation_sheet(ws, case1, case2, header_font, header_fill, border):
    """创建对账汇总页"""
    ws.title = "对账汇总页"

    # 标题
    ws['A1'] = "跨平台对账汇总与差异分析"
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:R1')

    # 第一部分：对账汇总表
    ws['A3'] = "一、对账汇总表"
    ws['A3'].font = Font(size=14, bold=True)

    # 对账汇总表头
    summary_headers = [
        "用户ID", "交易品种", "Bitda净盈亏", "Binance净盈亏", "对冲差异",
        "对冲成本", "对冲成本率", "风险敞口", "对冲状态"
    ]

    for col, header in enumerate(summary_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')

    # 案例1对账数据
    case1_summary = [
        case1['uid'],
        case1['symbol'],
        949.90,   # Bitda净盈亏
        816.32,   # Binance净盈亏
        -133.58,  # 对冲差异
        133.58,   # 对冲成本
        "0.243%", # 对冲成本率
        0,        # 风险敞口
        "✅ 完全对冲"
    ]

    # 案例2对账数据
    case2_summary = [
        case2['uid'],
        case2['symbol'],
        44.78,    # Bitda净盈亏
        0.50,     # Binance净盈亏
        -44.28,   # 对冲差异
        44.28,    # 对冲成本
        "1.265%", # 对冲成本率
        1737.5,   # 风险敞口
        "⚠️ 部分对冲"
    ]

    # 填充对账汇总数据
    for col, value in enumerate(case1_summary, 1):
        cell = ws.cell(row=6, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 2:
            cell.number_format = '#,##0.00'

    for col, value in enumerate(case2_summary, 1):
        cell = ws.cell(row=7, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 2:
            cell.number_format = '#,##0.00'

    # 第二部分：对冲成本明细分析
    ws['A10'] = "二、对冲成本明细分析"
    ws['A10'].font = Font(size=14, bold=True)

    # 添加说明
    ws['A11'] = "注：滑点成本等分析指标在此处展示，交易页面仅记录实际交易数据"
    ws['A11'].font = Font(size=10, italic=True, color="666666")

    # 成本明细表头
    cost_headers = [
        "用户ID", "交易品种", "价差损失", "滑点成本", "手续费差异",
        "资金费用差异", "时间成本", "总对冲成本", "成本占比"
    ]

    for col, header in enumerate(cost_headers, 1):
        cell = ws.cell(row=13, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')

    # 案例1成本明细
    case1_cost = [
        case1['uid'],
        case1['symbol'],
        65.00,    # 价差损失
        65.00,    # 滑点成本
        -11.13,   # 手续费差异
        0.29,     # 资金费用差异
        14.42,    # 时间成本
        133.58,   # 总对冲成本
        "0.243%"  # 成本占比
    ]

    # 案例2成本明细
    case2_cost = [
        case2['uid'],
        case2['symbol'],
        15.00,    # 价差损失
        30.00,    # 滑点成本
        0.71,     # 手续费差异
        0.01,     # 资金费用差异
        -1.44,    # 时间成本
        44.28,    # 总对冲成本
        "1.265%"  # 成本占比
    ]

    # 填充成本明细数据（调整行号）
    for col, value in enumerate(case1_cost, 1):
        cell = ws.cell(row=14, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 2:
            cell.number_format = '#,##0.00'

    for col, value in enumerate(case2_cost, 1):
        cell = ws.cell(row=15, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)) and col > 2:
            cell.number_format = '#,##0.00'

    # 第三部分：风险分析
    ws['A18'] = "三、风险分析"
    ws['A18'].font = Font(size=14, bold=True)

    # 风险分析表头
    risk_headers = [
        "用户ID", "对冲模式", "风险敞口(USDT)", "敞口比例", "市场风险",
        "流动性风险", "对冲延迟", "风险评级"
    ]

    for col, header in enumerate(risk_headers, 1):
        cell = ws.cell(row=20, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')

    # 风险分析数据
    case1_risk = [
        case1['uid'],
        "完全对冲",
        0,
        "0%",
        "🟢 低",
        "🟢 低",
        "150ms",
        "🟢 低风险"
    ]

    case2_risk = [
        case2['uid'],
        "部分对冲",
        1737.5,
        "8.69%",
        "🟡 中",
        "🟡 中",
        "180ms",
        "🟡 中风险"
    ]

    # 填充风险分析数据（调整行号）
    for col, value in enumerate(case1_risk, 1):
        cell = ws.cell(row=21, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)):
            cell.number_format = '#,##0.00'

    for col, value in enumerate(case2_risk, 1):
        cell = ws.cell(row=22, column=col, value=value)
        cell.border = border
        if isinstance(value, (int, float)):
            cell.number_format = '#,##0.00'

    # 第四部分：关键指标汇总
    ws['A25'] = "四、关键指标汇总"
    ws['A25'].font = Font(size=14, bold=True)

    # 关键指标
    indicators = [
        ["总交易量", "1.5", "BTC+ETH"],
        ["总交易金额", "58,500.00", "USDT"],
        ["总对冲成本", "177.86", "USDT"],
        ["平均对冲成本率", "0.754%", ""],
        ["对冲成功率", "100%", ""],
        ["平均对冲延迟", "165ms", ""],
        ["总风险敞口", "1,737.50", "USDT"],
        ["风险敞口比例", "2.97%", "相对总资金"]
    ]

    # 填充关键指标（调整行号）
    for row, (indicator, value, unit) in enumerate(indicators, 27):
        ws.cell(row=row, column=1, value=indicator).font = Font(bold=True)
        ws.cell(row=row, column=2, value=value)
        ws.cell(row=row, column=3, value=unit)

        # 添加边框
        for col in range(1, 4):
            ws.cell(row=row, column=col).border = border

    # 设置列宽
    column_widths = [15, 12, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

if __name__ == "__main__":
    create_hedge_reconciliation_excel()
