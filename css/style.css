/* 基础样式重置和全局设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 页面标题样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #e9ecef;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 400;
}

/* 查询表单样式 */
.query-section {
    margin-bottom: 30px;
}

.form-container {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input:hover,
.form-group select:hover {
    border-color: #adb5bd;
}

/* 查询按钮样式 */
.query-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.query-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.query-btn:active {
    transform: translateY(0);
}

.query-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 加载状态样式 */
.loading-indicator {
    text-align: center;
    padding: 40px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 20px 0;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误信息样式 */
.error-message {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid #dc3545;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #721c24;
}

.error-content h3 {
    margin-bottom: 10px;
    color: #dc3545;
}

.close-error-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
    transition: background 0.3s ease;
}

.close-error-btn:hover {
    background: #c82333;
}

/* 结果展示区域样式 */
.results-section {
    margin-top: 30px;
}

.chart-container,
.table-container,
.stats-container {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.chart-container h2,
.table-container h2,
.stats-container h2 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* 图表样式 */
.chart-wrapper {
    position: relative;
    height: 400px;
    margin-bottom: 15px;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.legend-color {
    width: 20px;
    height: 4px;
    border-radius: 2px;
}

.legend-color.bitda {
    background: #667eea;
}

.legend-color.binance {
    background: #f093fb;
}

/* 表格样式 */
.table-wrapper {
    overflow-x: auto;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.comparison-table th,
.comparison-table td {
    padding: 12px 15px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.comparison-table th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.comparison-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.comparison-table tbody tr:hover {
    background: #e9ecef;
    transition: background 0.3s ease;
}

.source-label {
    font-weight: 600;
    text-align: left !important;
}

.bitda-row .source-label {
    color: #667eea;
}

.binance-row .source-label {
    color: #e91e63;
}

.difference-row .source-label {
    color: #ff9800;
}

.percentage-row .source-label {
    color: #4caf50;
}

/* 统计信息样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

/* 无数据提示样式 */
.no-data-message {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 20px 0;
    border: 2px dashed #dee2e6;
}

.no-data-content h3 {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.no-data-content p {
    color: #adb5bd;
    font-size: 1rem;
}

/* 页脚样式 */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-top: 20px;
}
