/* 响应式布局样式 */

/* 大屏幕设备 (>=1200px) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
        padding: 30px;
    }
    
    .header h1 {
        font-size: 3rem;
    }
    
    .form-row {
        gap: 30px;
    }
    
    .chart-wrapper {
        height: 500px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 桌面设备 (1024px - 1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
    .container {
        max-width: 1000px;
        padding: 25px;
    }
    
    .header h1 {
        font-size: 2.5rem;
    }
    
    .chart-wrapper {
        height: 450px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 平板设备 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .container {
        margin: 10px;
        padding: 20px;
        border-radius: 10px;
    }
    
    .header h1 {
        font-size: 2.2rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .form-row {
        gap: 15px;
    }
    
    .form-container {
        padding: 20px;
    }
    
    .chart-wrapper {
        height: 350px;
    }
    
    .chart-legend {
        gap: 20px;
    }
    
    .comparison-table {
        font-size: 0.9rem;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 10px 12px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .stat-item {
        padding: 15px;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
}

/* 小平板和大手机 (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        margin: 5px;
        padding: 15px;
        border-radius: 8px;
    }
    
    .header {
        margin-bottom: 20px;
        padding: 15px 0;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .subtitle {
        font-size: 0.95rem;
    }
    
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .form-container {
        padding: 15px;
    }
    
    .query-btn {
        width: 100%;
        padding: 15px;
        font-size: 1.1rem;
    }
    
    .chart-wrapper {
        height: 300px;
    }
    
    .chart-legend {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .comparison-table {
        min-width: 600px;
        font-size: 0.85rem;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 8px 10px;
        white-space: nowrap;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .stat-item {
        padding: 12px;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
    
    .chart-container,
    .table-container,
    .stats-container {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .chart-container h2,
    .table-container h2,
    .stats-container h2 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }
}

/* 手机设备 (<576px) */
@media (max-width: 575px) {
    body {
        background: #667eea;
    }
    
    .container {
        margin: 0;
        padding: 10px;
        border-radius: 0;
        min-height: 100vh;
    }
    
    .header {
        margin-bottom: 15px;
        padding: 10px 0;
    }
    
    .header h1 {
        font-size: 1.5rem;
        line-height: 1.3;
    }
    
    .subtitle {
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .query-section {
        margin-bottom: 20px;
    }
    
    .form-container {
        padding: 12px;
        border-radius: 8px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 15px;
    }
    
    .form-group label {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }
    
    .form-group input,
    .form-group select {
        padding: 12px;
        font-size: 16px; /* 防止iOS缩放 */
        border-radius: 6px;
    }
    
    .query-btn {
        width: 100%;
        padding: 15px;
        font-size: 1rem;
        border-radius: 6px;
    }
    
    .loading-indicator {
        padding: 30px 15px;
        margin: 15px 0;
    }
    
    .loading-spinner {
        width: 30px;
        height: 30px;
        margin-bottom: 10px;
    }
    
    .error-message {
        padding: 15px;
        margin: 15px 0;
        border-radius: 6px;
    }
    
    .chart-wrapper {
        height: 250px;
    }
    
    .chart-legend {
        flex-direction: column;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
    }
    
    .legend-item {
        font-size: 0.9rem;
    }
    
    .table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin: -10px;
        padding: 10px;
    }
    
    .comparison-table {
        min-width: 500px;
        font-size: 0.8rem;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 6px 8px;
        white-space: nowrap;
    }
    
    .source-label {
        min-width: 80px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .stat-item {
        padding: 10px;
    }
    
    .stat-label {
        font-size: 0.8rem;
        margin-bottom: 6px;
    }
    
    .stat-value {
        font-size: 1.1rem;
    }
    
    .chart-container,
    .table-container,
    .stats-container {
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 8px;
    }
    
    .chart-container h2,
    .table-container h2,
    .stats-container h2 {
        font-size: 1.2rem;
        margin-bottom: 12px;
    }
    
    .no-data-message {
        padding: 40px 15px;
        margin: 15px 0;
    }
    
    .no-data-content h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
    }
    
    .no-data-content p {
        font-size: 0.9rem;
        line-height: 1.5;
    }
    
    .footer {
        padding: 15px 10px;
        font-size: 0.8rem;
        margin-top: 15px;
    }
}

/* 横屏手机优化 */
@media (max-width: 767px) and (orientation: landscape) {
    .chart-wrapper {
        height: 200px;
    }
    
    .form-row {
        flex-direction: row;
        gap: 10px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .loading-spinner {
        border-width: 2px;
    }
    
    .legend-color {
        height: 3px;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
    }
    
    .container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .query-section,
    .loading-indicator,
    .error-message {
        display: none;
    }
    
    .chart-wrapper {
        height: 300px;
    }
    
    .comparison-table {
        font-size: 0.8rem;
    }
    
    .footer {
        color: #333;
    }
}
