# 加密货币永续合约数据对比查询网页

## 项目简介

这是一个用于对比查询Bitda和Binance两个交易所加密货币永续合约数据的Web应用。用户可以选择时间范围和合约类型，查看两个平台的K线数据对比和详细的数据差异分析。

## 功能特性

### 🕒 时间选择器
- 支持精确到秒的时间选择
- 默认时间范围：过去24小时
- 自动验证时间范围（最大7天）
- 防止查询未来时间

### 📊 合约选择器
支持24个主流期货合约：
- BTCUSDT, ETHUSDT, SOLUSDT
- 1000PEPEUSDT, 1000SHIBUSDT
- ADAUSDT, AVAXUSDT, BCHUSDT
- DOGEUSDT, DOTUSDT, ETCUSDT
- LTCUSDT, MELANIAUSDT, OPUSDT
- SANDUSDT, SUIUSDT, TONUSDT
- TRUMPUSDT, UNIUSDT, XLMUSDT
- XRPUSDT, CRVUSDT, XTZUSDT, DYDXUSDT

### 📈 数据展示
1. **K线图对比**
   - 实时OHLC蜡烛图
   - 双平台数据叠加显示
   - 交互式图表缩放和工具提示
   - 支持显示/隐藏不同数据系列

2. **数据对比表格**
   - Bitda和Binance的OHLC数据
   - 差异值计算（Bitda - Binance）
   - 差异百分比计算
   - 成交量对比

3. **统计信息**
   - 数据点数量统计
   - 时间范围显示
   - 平均价差和最大价差

## 技术架构

### 前端技术栈
- **HTML5**: 语义化页面结构
- **CSS3**: 现代化样式和响应式布局
- **JavaScript (ES6+)**: 模块化业务逻辑
- **Chart.js**: 专业图表库
- **Fetch API**: 异步数据获取

### 核心模块
1. **API服务模块** (`js/api.js`)
   - Bitda和Binance API集成
   - 数据格式统一化
   - 错误处理和重试机制
   - 智能缓存系统

2. **图表管理模块** (`js/chart.js`)
   - Chart.js图表封装
   - 多数据系列管理
   - 交互功能实现
   - 图表导出功能

3. **工具函数模块** (`js/utils.js`)
   - 数据格式化和验证
   - 时间处理函数
   - 数学计算工具
   - 性能优化工具

4. **主业务逻辑** (`js/main.js`)
   - 用户交互处理
   - 数据流控制
   - 状态管理
   - UI更新协调

## 使用说明

### 启动应用
1. 确保所有文件在同一目录下
2. 启动本地HTTP服务器：
   ```bash
   python3 -m http.server 8000
   ```
3. 在浏览器中访问：`http://localhost:8000`

### 操作步骤
1. **选择时间范围**
   - 设置开始时间和结束时间
   - 时间精确到秒
   - 建议查询范围不超过24小时

2. **选择合约**
   - 从下拉菜单选择要查询的合约
   - 支持24个主流期货合约

3. **查询数据**
   - 点击"查询数据"按钮
   - 等待数据加载完成
   - 查看图表和表格结果

### 功能说明
- **图表交互**: 可以缩放、平移、显示/隐藏数据系列
- **数据对比**: 表格显示详细的价格差异和百分比
- **错误处理**: 友好的错误提示和重试机制
- **响应式设计**: 支持桌面、平板、手机等设备

## API数据源

### Bitda API
- **URL**: `https://api.bitda.com/open/api/v2/market/kline`
- **参数**: `market={symbol}&type=1min`
- **频率**: 1分钟K线数据

### Binance API
- **URL**: `https://fapi.binance.com/fapi/v1/klines`
- **参数**: `symbol={symbol}&interval=1m&startTime={start}&endTime={end}`
- **频率**: 1分钟K线数据

## 性能优化

### 缓存机制
- 浏览器内存缓存API响应
- 避免重复请求相同数据
- 智能缓存键管理

### 网络优化
- 并行请求两个API
- 10秒请求超时设置
- 错误重试机制

### 渲染优化
- 图表数据懒加载
- DOM操作批量处理
- 响应式图表尺寸

## 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### 移动端支持
- ✅ iOS Safari 13+
- ✅ Android Chrome 80+
- ✅ 响应式布局适配

## 故障排除

### 常见问题
1. **数据加载失败**
   - 检查网络连接
   - 确认API服务可用性
   - 尝试缩小时间范围

2. **图表显示异常**
   - 刷新页面重试
   - 检查浏览器控制台错误
   - 确认Chart.js库加载成功

3. **时间选择问题**
   - 确保开始时间早于结束时间
   - 避免选择未来时间
   - 时间范围不超过7天

### 调试模式
打开浏览器开发者工具，查看控制台日志：
- API请求状态
- 数据处理过程
- 错误详细信息

## 项目结构

```
futures/
├── index.html              # 主页面
├── css/
│   ├── style.css           # 主样式文件
│   └── responsive.css      # 响应式样式
├── js/
│   ├── main.js             # 主业务逻辑
│   ├── api.js              # API数据获取
│   ├── chart.js            # 图表功能
│   └── utils.js            # 工具函数
├── assets/
│   └── images/             # 图片资源
└── doc/
    ├── project-plan.md     # 项目规划
    └── task-list.md        # 任务列表
```

## 开发说明

### 代码规范
- ES6+语法标准
- 模块化设计
- 详细注释文档
- 错误处理完善

### 扩展建议
1. 添加更多交易所支持
2. 实现数据导出功能
3. 增加技术指标分析
4. 添加实时数据推送

## 许可证

本项目仅供学习和研究使用，请遵守相关API的使用条款。

---

**注意**: 本应用依赖第三方API服务，数据准确性和可用性取决于API提供商。建议在生产环境中添加更多的错误处理和监控机制。
