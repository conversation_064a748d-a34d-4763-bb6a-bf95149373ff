<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密货币永续合约数据对比查询</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <header class="header">
            <h1>加密货币永续合约数据对比查询</h1>
            <p class="subtitle">Bitda vs Binance 期货合约数据实时对比分析</p>
        </header>

        <!-- 查询表单 -->
        <section class="query-section">
            <div class="form-container">
                <div class="form-row">
                    <div class="form-group">
                        <label for="startTime">开始时间：</label>
                        <input type="datetime-local" id="startTime" name="startTime" 
                               value="2025-07-29T00:00:00" step="1" required>
                    </div>
                    <div class="form-group">
                        <label for="endTime">结束时间：</label>
                        <input type="datetime-local" id="endTime" name="endTime" 
                               value="2025-07-30T00:00:00" step="1" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="contractSelect">选择合约：</label>
                        <select id="contractSelect" name="contract" required>
                            <option value="">请选择合约</option>
                            <option value="BTCUSDT">BTCUSDT</option>
                            <option value="ETHUSDT">ETHUSDT</option>
                            <option value="SOLUSDT">SOLUSDT</option>
                            <option value="1000PEPEUSDT">1000PEPEUSDT</option>
                            <option value="1000SHIBUSDT">1000SHIBUSDT</option>
                            <option value="ADAUSDT">ADAUSDT</option>
                            <option value="AVAXUSDT">AVAXUSDT</option>
                            <option value="BCHUSDT">BCHUSDT</option>
                            <option value="DOGEUSDT">DOGEUSDT</option>
                            <option value="DOTUSDT">DOTUSDT</option>
                            <option value="ETCUSDT">ETCUSDT</option>
                            <option value="LTCUSDT">LTCUSDT</option>
                            <option value="MELANIAUSDT">MELANIAUSDT</option>
                            <option value="OPUSDT">OPUSDT</option>
                            <option value="SANDUSDT">SANDUSDT</option>
                            <option value="SUIUSDT">SUIUSDT</option>
                            <option value="TONUSDT">TONUSDT</option>
                            <option value="TRUMPUSDT">TRUMPUSDT</option>
                            <option value="UNIUSDT">UNIUSDT</option>
                            <option value="XLMUSDT">XLMUSDT</option>
                            <option value="XRPUSDT">XRPUSDT</option>
                            <option value="CRVUSDT">CRVUSDT</option>
                            <option value="XTZUSDT">XTZUSDT</option>
                            <option value="DYDXUSDT">DYDXUSDT</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="button" id="queryBtn" class="query-btn">
                            <span class="btn-text">查询数据</span>
                            <span class="btn-loading" style="display: none;">查询中...</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 加载状态提示 -->
        <div id="loadingIndicator" class="loading-indicator" style="display: none;">
            <div class="loading-spinner"></div>
            <p>正在获取数据，请稍候...</p>
        </div>

        <!-- 错误信息提示 -->
        <div id="errorMessage" class="error-message" style="display: none;">
            <div class="error-content">
                <h3>查询出错</h3>
                <p id="errorText"></p>
                <button type="button" id="closeError" class="close-error-btn">关闭</button>
            </div>
        </div>

        <!-- 结果展示区域 -->
        <section id="resultsSection" class="results-section" style="display: none;">
            <!-- K线图表展示 -->
            <div class="chart-container">
                <h2>K线图对比</h2>
                <div class="chart-wrapper">
                    <canvas id="priceChart"></canvas>
                </div>
                <div class="chart-legend">
                    <div class="legend-item">
                        <span class="legend-color bitda"></span>
                        <span>Bitda</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color binance"></span>
                        <span>Binance</span>
                    </div>
                </div>
            </div>

            <!-- 数据对比表格 -->
            <div class="table-container">
                <h2>数据对比分析</h2>
                <div class="table-wrapper">
                    <table id="comparisonTable" class="comparison-table">
                        <thead>
                            <tr>
                                <th>数据源</th>
                                <th>开盘价</th>
                                <th>最高价</th>
                                <th>最低价</th>
                                <th>收盘价</th>
                                <th>成交量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="bitda-row">
                                <td class="source-label">Bitda</td>
                                <td id="bitdaOpen">-</td>
                                <td id="bitdaHigh">-</td>
                                <td id="bitdaLow">-</td>
                                <td id="bitdaClose">-</td>
                                <td id="bitdaVolume">-</td>
                            </tr>
                            <tr class="binance-row">
                                <td class="source-label">Binance</td>
                                <td id="binanceOpen">-</td>
                                <td id="binanceHigh">-</td>
                                <td id="binanceLow">-</td>
                                <td id="binanceClose">-</td>
                                <td id="binanceVolume">-</td>
                            </tr>
                            <tr class="difference-row">
                                <td class="source-label">差异值</td>
                                <td id="diffOpen">-</td>
                                <td id="diffHigh">-</td>
                                <td id="diffLow">-</td>
                                <td id="diffClose">-</td>
                                <td id="diffVolume">-</td>
                            </tr>
                            <tr class="percentage-row">
                                <td class="source-label">差异百分比</td>
                                <td id="percentOpen">-</td>
                                <td id="percentHigh">-</td>
                                <td id="percentLow">-</td>
                                <td id="percentClose">-</td>
                                <td id="percentVolume">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats-container">
                <h2>统计信息</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">数据点数量</span>
                        <span id="dataPointsCount" class="stat-value">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">时间范围</span>
                        <span id="timeRange" class="stat-value">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">平均价差</span>
                        <span id="avgPriceDiff" class="stat-value">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最大价差</span>
                        <span id="maxPriceDiff" class="stat-value">-</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 无数据提示 -->
        <div id="noDataMessage" class="no-data-message" style="display: none;">
            <div class="no-data-content">
                <h3>暂无数据</h3>
                <p>在指定的时间范围内未找到相关数据，请尝试调整查询条件。</p>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <p>&copy; 2025 加密货币数据对比工具. 数据来源：Bitda & Binance</p>
    </footer>

    <!-- JavaScript 文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/chart.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
