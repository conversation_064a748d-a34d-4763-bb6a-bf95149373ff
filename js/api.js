/**
 * API数据获取模块
 * 负责从Bitda和Binance获取K线数据
 */

class APIService {
    constructor() {
        this.cache = new Map();
        this.requestTimeout = 10000; // 10秒超时
    }

    /**
     * 获取Bitda API数据
     * @param {string} symbol - 交易对符号
     * @param {string} startTime - 开始时间 (ISO格式)
     * @param {string} endTime - 结束时间 (ISO格式)
     * @returns {Promise<Array>} 格式化后的K线数据
     */
    async getBitdaData(symbol, startTime, endTime) {
        try {
            const cacheKey = `bitda_${symbol}_${startTime}_${endTime}`;
            
            // 检查缓存
            if (this.cache.has(cacheKey)) {
                console.log('从缓存获取Bitda数据:', symbol);
                return this.cache.get(cacheKey);
            }

            console.log('获取Bitda数据:', symbol, startTime, endTime);
            
            const url = `https://api.bitda.com/open/api/v2/market/kline?market=${symbol}&type=1min`;
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`Bitda API请求失败: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.code !== 0) {
                throw new Error(`Bitda API错误: ${data.msg || '未知错误'}`);
            }
            
            if (!data.data || !Array.isArray(data.data)) {
                throw new Error('Bitda API返回数据格式错误');
            }
            
            // 格式化数据
            const formattedData = this.formatBitdaData(data.data, startTime, endTime);
            
            // 缓存数据
            this.cache.set(cacheKey, formattedData);
            
            console.log(`Bitda数据获取成功: ${formattedData.length}条记录`);
            return formattedData;
            
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Bitda API请求超时，请检查网络连接');
            }
            console.error('Bitda API错误:', error);
            throw new Error(`获取Bitda数据失败: ${error.message}`);
        }
    }

    /**
     * 获取Binance API数据
     * @param {string} symbol - 交易对符号
     * @param {string} startTime - 开始时间 (ISO格式)
     * @param {string} endTime - 结束时间 (ISO格式)
     * @returns {Promise<Array>} 格式化后的K线数据
     */
    async getBinanceData(symbol, startTime, endTime) {
        try {
            const cacheKey = `binance_${symbol}_${startTime}_${endTime}`;
            
            // 检查缓存
            if (this.cache.has(cacheKey)) {
                console.log('从缓存获取Binance数据:', symbol);
                return this.cache.get(cacheKey);
            }

            console.log('获取Binance数据:', symbol, startTime, endTime);
            
            // 转换时间为时间戳
            const startTimestamp = new Date(startTime).getTime();
            const endTimestamp = new Date(endTime).getTime();
            
            const url = `https://fapi.binance.com/fapi/v1/klines?symbol=${symbol}&interval=1m&startTime=${startTimestamp}&endTime=${endTimestamp}&limit=1500`;
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                },
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`Binance API请求失败: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (!Array.isArray(data)) {
                throw new Error('Binance API返回数据格式错误');
            }
            
            // 格式化数据
            const formattedData = this.formatBinanceData(data);
            
            // 缓存数据
            this.cache.set(cacheKey, formattedData);
            
            console.log(`Binance数据获取成功: ${formattedData.length}条记录`);
            return formattedData;
            
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Binance API请求超时，请检查网络连接');
            }
            console.error('Binance API错误:', error);
            throw new Error(`获取Binance数据失败: ${error.message}`);
        }
    }

    /**
     * 格式化Bitda数据
     * @param {Array} rawData - 原始数据
     * @param {string} startTime - 开始时间
     * @param {string} endTime - 结束时间
     * @returns {Array} 格式化后的数据
     */
    formatBitdaData(rawData, startTime, endTime) {
        const startTimestamp = new Date(startTime).getTime();
        const endTimestamp = new Date(endTime).getTime();
        
        return rawData
            .filter(item => {
                const itemTime = parseInt(item.time);
                return itemTime >= startTimestamp && itemTime <= endTimestamp;
            })
            .map(item => ({
                timestamp: parseInt(item.time),
                time: new Date(parseInt(item.time)).toISOString(),
                open: parseFloat(item.open),
                high: parseFloat(item.high),
                low: parseFloat(item.low),
                close: parseFloat(item.close),
                volume: parseFloat(item.volume || 0)
            }))
            .sort((a, b) => a.timestamp - b.timestamp);
    }

    /**
     * 格式化Binance数据
     * @param {Array} rawData - 原始数据
     * @returns {Array} 格式化后的数据
     */
    formatBinanceData(rawData) {
        return rawData.map(item => ({
            timestamp: parseInt(item[0]),
            time: new Date(parseInt(item[0])).toISOString(),
            open: parseFloat(item[1]),
            high: parseFloat(item[2]),
            low: parseFloat(item[3]),
            close: parseFloat(item[4]),
            volume: parseFloat(item[5])
        }))
        .sort((a, b) => a.timestamp - b.timestamp);
    }

    /**
     * 同时获取两个平台的数据
     * @param {string} symbol - 交易对符号
     * @param {string} startTime - 开始时间
     * @param {string} endTime - 结束时间
     * @returns {Promise<Object>} 包含两个平台数据的对象
     */
    async getBothPlatformsData(symbol, startTime, endTime) {
        try {
            console.log('开始获取双平台数据:', symbol);
            
            // 并行获取两个平台的数据
            const [bitdaData, binanceData] = await Promise.allSettled([
                this.getBitdaData(symbol, startTime, endTime),
                this.getBinanceData(symbol, startTime, endTime)
            ]);
            
            const result = {
                bitda: {
                    success: bitdaData.status === 'fulfilled',
                    data: bitdaData.status === 'fulfilled' ? bitdaData.value : [],
                    error: bitdaData.status === 'rejected' ? bitdaData.reason.message : null
                },
                binance: {
                    success: binanceData.status === 'fulfilled',
                    data: binanceData.status === 'fulfilled' ? binanceData.value : [],
                    error: binanceData.status === 'rejected' ? binanceData.reason.message : null
                }
            };
            
            // 检查是否至少有一个平台成功
            if (!result.bitda.success && !result.binance.success) {
                throw new Error('两个平台的数据都获取失败');
            }
            
            console.log('双平台数据获取完成:', {
                bitda: result.bitda.success ? `${result.bitda.data.length}条` : '失败',
                binance: result.binance.success ? `${result.binance.data.length}条` : '失败'
            });
            
            return result;
            
        } catch (error) {
            console.error('获取双平台数据失败:', error);
            throw error;
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
        console.log('API缓存已清除');
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

// 创建全局API服务实例
window.apiService = new APIService();
