/**
 * 工具函数模块
 * 提供时间处理、数据计算、格式化等通用功能
 */

class Utils {
    /**
     * 格式化数字，保留指定小数位
     * @param {number} num - 要格式化的数字
     * @param {number} decimals - 小数位数，默认4位
     * @returns {string} 格式化后的字符串
     */
    static formatNumber(num, decimals = 4) {
        if (num === null || num === undefined || isNaN(num)) {
            return '-';
        }
        return parseFloat(num).toFixed(decimals);
    }

    /**
     * 格式化百分比
     * @param {number} num - 要格式化的数字
     * @param {number} decimals - 小数位数，默认2位
     * @returns {string} 格式化后的百分比字符串
     */
    static formatPercentage(num, decimals = 2) {
        if (num === null || num === undefined || isNaN(num)) {
            return '-';
        }
        const formatted = parseFloat(num).toFixed(decimals);
        return `${formatted}%`;
    }

    /**
     * 格式化成交量
     * @param {number} volume - 成交量
     * @returns {string} 格式化后的成交量字符串
     */
    static formatVolume(volume) {
        if (volume === null || volume === undefined || isNaN(volume)) {
            return '-';
        }
        
        if (volume >= 1000000) {
            return (volume / 1000000).toFixed(2) + 'M';
        } else if (volume >= 1000) {
            return (volume / 1000).toFixed(2) + 'K';
        } else {
            return volume.toFixed(2);
        }
    }

    /**
     * 格式化时间戳为可读时间
     * @param {number} timestamp - 时间戳
     * @returns {string} 格式化后的时间字符串
     */
    static formatTimestamp(timestamp) {
        if (!timestamp) return '-';
        
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 将datetime-local输入值转换为ISO字符串
     * @param {string} datetimeLocal - datetime-local输入值
     * @returns {string} ISO格式时间字符串
     */
    static datetimeLocalToISO(datetimeLocal) {
        if (!datetimeLocal) return null;
        return new Date(datetimeLocal).toISOString();
    }

    /**
     * 将ISO时间字符串转换为datetime-local格式
     * @param {string} isoString - ISO格式时间字符串
     * @returns {string} datetime-local格式字符串
     */
    static isoToDatetimeLocal(isoString) {
        if (!isoString) return '';
        const date = new Date(isoString);
        const offset = date.getTimezoneOffset() * 60000;
        const localDate = new Date(date.getTime() - offset);
        return localDate.toISOString().slice(0, 19);
    }

    /**
     * 计算两个数值的差异
     * @param {number} value1 - 第一个值
     * @param {number} value2 - 第二个值
     * @returns {number} 差异值 (value1 - value2)
     */
    static calculateDifference(value1, value2) {
        if (value1 === null || value1 === undefined || isNaN(value1) ||
            value2 === null || value2 === undefined || isNaN(value2)) {
            return null;
        }
        return value1 - value2;
    }

    /**
     * 计算百分比差异
     * @param {number} value1 - 第一个值
     * @param {number} value2 - 第二个值（基准值）
     * @returns {number} 百分比差异
     */
    static calculatePercentageDifference(value1, value2) {
        if (value1 === null || value1 === undefined || isNaN(value1) ||
            value2 === null || value2 === undefined || isNaN(value2) || value2 === 0) {
            return null;
        }
        return ((value1 - value2) / value2) * 100;
    }

    /**
     * 验证时间范围
     * @param {string} startTime - 开始时间
     * @param {string} endTime - 结束时间
     * @returns {Object} 验证结果
     */
    static validateTimeRange(startTime, endTime) {
        const result = {
            valid: true,
            error: null
        };

        if (!startTime || !endTime) {
            result.valid = false;
            result.error = '请选择开始时间和结束时间';
            return result;
        }

        const start = new Date(startTime);
        const end = new Date(endTime);

        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            result.valid = false;
            result.error = '时间格式无效';
            return result;
        }

        if (start >= end) {
            result.valid = false;
            result.error = '开始时间必须早于结束时间';
            return result;
        }

        // 检查时间范围是否过大（超过7天）
        const diffDays = (end - start) / (1000 * 60 * 60 * 24);
        if (diffDays > 7) {
            result.valid = false;
            result.error = '时间范围不能超过7天';
            return result;
        }

        // 检查是否是未来时间
        const now = new Date();
        if (start > now || end > now) {
            result.valid = false;
            result.error = '不能查询未来时间的数据';
            return result;
        }

        return result;
    }

    /**
     * 过滤数据数组，保留指定时间范围内的数据
     * @param {Array} data - 数据数组
     * @param {string} startTime - 开始时间
     * @param {string} endTime - 结束时间
     * @returns {Array} 过滤后的数据数组
     */
    static filterDataByTimeRange(data, startTime, endTime) {
        if (!Array.isArray(data) || !startTime || !endTime) {
            return [];
        }

        const startTimestamp = new Date(startTime).getTime();
        const endTimestamp = new Date(endTime).getTime();

        return data.filter(item => {
            const itemTimestamp = item.timestamp || new Date(item.time).getTime();
            return itemTimestamp >= startTimestamp && itemTimestamp <= endTimestamp;
        });
    }

    /**
     * 计算数组的统计信息
     * @param {Array} data - 数据数组
     * @param {string} field - 要统计的字段名
     * @returns {Object} 统计信息
     */
    static calculateStats(data, field) {
        if (!Array.isArray(data) || data.length === 0) {
            return {
                count: 0,
                min: null,
                max: null,
                avg: null,
                sum: null
            };
        }

        const values = data.map(item => parseFloat(item[field])).filter(val => !isNaN(val));
        
        if (values.length === 0) {
            return {
                count: 0,
                min: null,
                max: null,
                avg: null,
                sum: null
            };
        }

        const sum = values.reduce((acc, val) => acc + val, 0);
        
        return {
            count: values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            avg: sum / values.length,
            sum: sum
        };
    }

    /**
     * 对齐两个数据数组的时间戳
     * @param {Array} data1 - 第一个数据数组
     * @param {Array} data2 - 第二个数据数组
     * @returns {Object} 对齐后的数据
     */
    static alignDataByTimestamp(data1, data2) {
        if (!Array.isArray(data1) || !Array.isArray(data2)) {
            return { aligned1: [], aligned2: [], commonTimestamps: [] };
        }

        // 创建时间戳映射
        const map1 = new Map();
        const map2 = new Map();

        data1.forEach(item => {
            map1.set(item.timestamp, item);
        });

        data2.forEach(item => {
            map2.set(item.timestamp, item);
        });

        // 找到共同的时间戳
        const commonTimestamps = [...map1.keys()].filter(timestamp => map2.has(timestamp));
        commonTimestamps.sort((a, b) => a - b);

        // 构建对齐的数据数组
        const aligned1 = commonTimestamps.map(timestamp => map1.get(timestamp));
        const aligned2 = commonTimestamps.map(timestamp => map2.get(timestamp));

        return {
            aligned1,
            aligned2,
            commonTimestamps
        };
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
    }
}

// 将Utils类添加到全局作用域
window.Utils = Utils;
