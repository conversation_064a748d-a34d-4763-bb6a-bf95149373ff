/**
 * 图表功能模块
 * 使用Chart.js实现K线图表展示和双平台数据对比
 */

class ChartManager {
    constructor() {
        this.chart = null;
        this.chartCanvas = null;
        this.chartConfig = null;
    }

    /**
     * 初始化图表
     */
    init() {
        this.chartCanvas = document.getElementById('priceChart');
        if (!this.chartCanvas) {
            console.error('图表画布元素未找到');
            return;
        }

        // 设置默认配置
        this.setupDefaultConfig();
    }

    /**
     * 设置默认图表配置
     */
    setupDefaultConfig() {
        this.chartConfig = {
            type: 'line',
            data: {
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'K线价格对比图表',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: true,
                        callbacks: {
                            title: function(context) {
                                if (context[0] && context[0].parsed && context[0].parsed.x) {
                                    return Utils.formatTimestamp(context[0].parsed.x);
                                }
                                return '';
                            },
                            label: function(context) {
                                const datasetLabel = context.dataset.label || '';
                                const value = context.parsed.y;
                                return `${datasetLabel}: ${Utils.formatNumber(value, 4)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'MM-dd HH:mm'
                            }
                        },
                        title: {
                            display: true,
                            text: '时间'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '价格 (USDT)'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return Utils.formatNumber(value, 4);
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 2,
                        hoverRadius: 4
                    },
                    line: {
                        borderWidth: 2,
                        tension: 0.1
                    }
                }
            }
        };
    }

    /**
     * 创建或更新图表
     * @param {Object} bitdaData - Bitda数据
     * @param {Object} binanceData - Binance数据
     * @param {string} symbol - 交易对符号
     */
    updateChart(bitdaData, binanceData, symbol) {
        try {
            console.log('更新图表数据:', symbol);

            // 准备数据集
            const datasets = [];

            // 添加Bitda数据集
            if (bitdaData && bitdaData.success && bitdaData.data.length > 0) {
                datasets.push(this.createDataset('Bitda', bitdaData.data, '#667eea', 'close'));
                datasets.push(this.createDataset('Bitda (开盘)', bitdaData.data, '#667eea', 'open', true));
                datasets.push(this.createDataset('Bitda (最高)', bitdaData.data, '#4c63d2', 'high', true));
                datasets.push(this.createDataset('Bitda (最低)', bitdaData.data, '#8b9dc3', 'low', true));
            }

            // 添加Binance数据集
            if (binanceData && binanceData.success && binanceData.data.length > 0) {
                datasets.push(this.createDataset('Binance', binanceData.data, '#f093fb', 'close'));
                datasets.push(this.createDataset('Binance (开盘)', binanceData.data, '#f093fb', 'open', true));
                datasets.push(this.createDataset('Binance (最高)', binanceData.data, '#e91e63', 'high', true));
                datasets.push(this.createDataset('Binance (最低)', binanceData.data, '#f8bbd9', 'low', true));
            }

            if (datasets.length === 0) {
                this.showNoDataChart();
                return;
            }

            // 更新图表配置
            this.chartConfig.data.datasets = datasets;
            this.chartConfig.options.plugins.title.text = `${symbol} K线价格对比图表`;

            // 销毁现有图表
            if (this.chart) {
                this.chart.destroy();
            }

            // 创建新图表
            this.chart = new Chart(this.chartCanvas, this.chartConfig);

            console.log('图表更新完成');

        } catch (error) {
            console.error('更新图表失败:', error);
            this.showErrorChart(error.message);
        }
    }

    /**
     * 创建数据集
     * @param {string} label - 数据集标签
     * @param {Array} data - 数据数组
     * @param {string} color - 颜色
     * @param {string} field - 数据字段
     * @param {boolean} hidden - 是否默认隐藏
     * @returns {Object} 数据集对象
     */
    createDataset(label, data, color, field, hidden = false) {
        const chartData = data.map(item => ({
            x: item.timestamp,
            y: item[field]
        }));

        return {
            label: label,
            data: chartData,
            borderColor: color,
            backgroundColor: color + '20',
            fill: false,
            hidden: hidden,
            pointBackgroundColor: color,
            pointBorderColor: color,
            pointHoverBackgroundColor: color,
            pointHoverBorderColor: '#ffffff',
            pointHoverBorderWidth: 2
        };
    }

    /**
     * 显示无数据图表
     */
    showNoDataChart() {
        if (this.chart) {
            this.chart.destroy();
        }

        const noDataConfig = {
            type: 'line',
            data: {
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '暂无图表数据',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                }
            }
        };

        this.chart = new Chart(this.chartCanvas, noDataConfig);
    }

    /**
     * 显示错误图表
     * @param {string} errorMessage - 错误信息
     */
    showErrorChart(errorMessage) {
        if (this.chart) {
            this.chart.destroy();
        }

        const errorConfig = {
            type: 'line',
            data: {
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `图表加载失败: ${errorMessage}`,
                        font: {
                            size: 14,
                            weight: 'bold'
                        },
                        color: '#dc3545'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                }
            }
        };

        this.chart = new Chart(this.chartCanvas, errorConfig);
    }

    /**
     * 清除图表
     */
    clearChart() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
    }

    /**
     * 重置图表缩放
     */
    resetZoom() {
        if (this.chart) {
            this.chart.resetZoom();
        }
    }

    /**
     * 导出图表为图片
     * @param {string} filename - 文件名
     */
    exportChart(filename = 'chart.png') {
        if (!this.chart) {
            console.warn('没有可导出的图表');
            return;
        }

        try {
            const url = this.chart.toBase64Image();
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            link.click();
        } catch (error) {
            console.error('导出图表失败:', error);
        }
    }

    /**
     * 获取图表实例
     * @returns {Chart} Chart.js实例
     */
    getChart() {
        return this.chart;
    }

    /**
     * 检查图表是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInitialized() {
        return this.chart !== null;
    }
}

// 创建全局图表管理器实例
window.chartManager = new ChartManager();
