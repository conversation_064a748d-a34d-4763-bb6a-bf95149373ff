/**
 * 主要业务逻辑模块
 * 负责整合所有功能模块，处理用户交互和数据流控制
 */

class CryptoComparisonApp {
    constructor() {
        this.isQuerying = false;
        this.currentData = null;
        this.elements = {};
        
        // 绑定方法的this上下文
        this.handleQuery = this.handleQuery.bind(this);
        this.handleCloseError = this.handleCloseError.bind(this);
    }

    /**
     * 初始化应用
     */
    init() {
        console.log('初始化加密货币对比应用...');
        
        // 获取DOM元素
        this.initElements();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 初始化图表管理器
        chartManager.init();
        
        // 设置默认时间
        this.setDefaultTime();
        
        console.log('应用初始化完成');
    }

    /**
     * 初始化DOM元素引用
     */
    initElements() {
        this.elements = {
            // 表单元素
            startTime: document.getElementById('startTime'),
            endTime: document.getElementById('endTime'),
            contractSelect: document.getElementById('contractSelect'),
            queryBtn: document.getElementById('queryBtn'),
            
            // 状态提示元素
            loadingIndicator: document.getElementById('loadingIndicator'),
            errorMessage: document.getElementById('errorMessage'),
            errorText: document.getElementById('errorText'),
            closeError: document.getElementById('closeError'),
            noDataMessage: document.getElementById('noDataMessage'),
            resultsSection: document.getElementById('resultsSection'),
            
            // 表格元素
            comparisonTable: document.getElementById('comparisonTable'),
            bitdaOpen: document.getElementById('bitdaOpen'),
            bitdaHigh: document.getElementById('bitdaHigh'),
            bitdaLow: document.getElementById('bitdaLow'),
            bitdaClose: document.getElementById('bitdaClose'),
            bitdaVolume: document.getElementById('bitdaVolume'),
            binanceOpen: document.getElementById('binanceOpen'),
            binanceHigh: document.getElementById('binanceHigh'),
            binanceLow: document.getElementById('binanceLow'),
            binanceClose: document.getElementById('binanceClose'),
            binanceVolume: document.getElementById('binanceVolume'),
            diffOpen: document.getElementById('diffOpen'),
            diffHigh: document.getElementById('diffHigh'),
            diffLow: document.getElementById('diffLow'),
            diffClose: document.getElementById('diffClose'),
            diffVolume: document.getElementById('diffVolume'),
            percentOpen: document.getElementById('percentOpen'),
            percentHigh: document.getElementById('percentHigh'),
            percentLow: document.getElementById('percentLow'),
            percentClose: document.getElementById('percentClose'),
            percentVolume: document.getElementById('percentVolume'),
            
            // 统计信息元素
            dataPointsCount: document.getElementById('dataPointsCount'),
            timeRange: document.getElementById('timeRange'),
            avgPriceDiff: document.getElementById('avgPriceDiff'),
            maxPriceDiff: document.getElementById('maxPriceDiff')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 查询按钮点击事件
        if (this.elements.queryBtn) {
            this.elements.queryBtn.addEventListener('click', this.handleQuery);
        }
        
        // 错误关闭按钮点击事件
        if (this.elements.closeError) {
            this.elements.closeError.addEventListener('click', this.handleCloseError);
        }
        
        // 表单验证事件
        [this.elements.startTime, this.elements.endTime, this.elements.contractSelect].forEach(element => {
            if (element) {
                element.addEventListener('change', this.validateForm.bind(this));
            }
        });
    }

    /**
     * 设置默认时间
     */
    setDefaultTime() {
        const now = new Date();
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        
        if (this.elements.startTime) {
            this.elements.startTime.value = Utils.isoToDatetimeLocal(yesterday.toISOString());
        }
        
        if (this.elements.endTime) {
            this.elements.endTime.value = Utils.isoToDatetimeLocal(now.toISOString());
        }
    }

    /**
     * 验证表单
     */
    validateForm() {
        const startTime = this.elements.startTime?.value;
        const endTime = this.elements.endTime?.value;
        const contract = this.elements.contractSelect?.value;
        
        const isValid = startTime && endTime && contract;
        
        if (this.elements.queryBtn) {
            this.elements.queryBtn.disabled = !isValid || this.isQuerying;
        }
        
        return isValid;
    }

    /**
     * 处理查询按钮点击
     */
    async handleQuery() {
        if (this.isQuerying || !this.validateForm()) {
            return;
        }

        try {
            // 获取表单数据
            const startTime = Utils.datetimeLocalToISO(this.elements.startTime.value);
            const endTime = Utils.datetimeLocalToISO(this.elements.endTime.value);
            const contract = this.elements.contractSelect.value;

            // 验证时间范围
            const timeValidation = Utils.validateTimeRange(startTime, endTime);
            if (!timeValidation.valid) {
                this.showError(timeValidation.error);
                return;
            }

            console.log('开始查询:', { contract, startTime, endTime });

            // 设置查询状态
            this.setQueryingState(true);
            this.hideAllMessages();

            // 获取数据
            const data = await apiService.getBothPlatformsData(contract, startTime, endTime);
            
            // 保存当前数据
            this.currentData = data;

            // 检查是否有有效数据
            if (!data.bitda.success && !data.binance.success) {
                throw new Error('两个平台的数据都获取失败');
            }

            if (data.bitda.data.length === 0 && data.binance.data.length === 0) {
                this.showNoData();
                return;
            }

            // 更新图表
            chartManager.updateChart(data.bitda, data.binance, contract);

            // 更新表格
            this.updateComparisonTable(data);

            // 更新统计信息
            this.updateStatistics(data, startTime, endTime);

            // 显示结果
            this.showResults();

            console.log('查询完成');

        } catch (error) {
            console.error('查询失败:', error);
            this.showError(error.message);
        } finally {
            this.setQueryingState(false);
        }
    }

    /**
     * 更新数据对比表格
     * @param {Object} data - 包含两个平台数据的对象
     */
    updateComparisonTable(data) {
        try {
            // 计算汇总数据
            const bitdaSummary = this.calculateSummaryData(data.bitda.data);
            const binanceSummary = this.calculateSummaryData(data.binance.data);

            // 更新Bitda数据行
            this.elements.bitdaOpen.textContent = data.bitda.success ? Utils.formatNumber(bitdaSummary.open) : '-';
            this.elements.bitdaHigh.textContent = data.bitda.success ? Utils.formatNumber(bitdaSummary.high) : '-';
            this.elements.bitdaLow.textContent = data.bitda.success ? Utils.formatNumber(bitdaSummary.low) : '-';
            this.elements.bitdaClose.textContent = data.bitda.success ? Utils.formatNumber(bitdaSummary.close) : '-';
            this.elements.bitdaVolume.textContent = data.bitda.success ? Utils.formatVolume(bitdaSummary.volume) : '-';

            // 更新Binance数据行
            this.elements.binanceOpen.textContent = data.binance.success ? Utils.formatNumber(binanceSummary.open) : '-';
            this.elements.binanceHigh.textContent = data.binance.success ? Utils.formatNumber(binanceSummary.high) : '-';
            this.elements.binanceLow.textContent = data.binance.success ? Utils.formatNumber(binanceSummary.low) : '-';
            this.elements.binanceClose.textContent = data.binance.success ? Utils.formatNumber(binanceSummary.close) : '-';
            this.elements.binanceVolume.textContent = data.binance.success ? Utils.formatVolume(binanceSummary.volume) : '-';

            // 计算并更新差异值
            if (data.bitda.success && data.binance.success) {
                const diffOpen = Utils.calculateDifference(bitdaSummary.open, binanceSummary.open);
                const diffHigh = Utils.calculateDifference(bitdaSummary.high, binanceSummary.high);
                const diffLow = Utils.calculateDifference(bitdaSummary.low, binanceSummary.low);
                const diffClose = Utils.calculateDifference(bitdaSummary.close, binanceSummary.close);
                const diffVolume = Utils.calculateDifference(bitdaSummary.volume, binanceSummary.volume);

                this.elements.diffOpen.textContent = Utils.formatNumber(diffOpen);
                this.elements.diffHigh.textContent = Utils.formatNumber(diffHigh);
                this.elements.diffLow.textContent = Utils.formatNumber(diffLow);
                this.elements.diffClose.textContent = Utils.formatNumber(diffClose);
                this.elements.diffVolume.textContent = Utils.formatVolume(diffVolume);

                // 计算并更新差异百分比
                const percentOpen = Utils.calculatePercentageDifference(bitdaSummary.open, binanceSummary.open);
                const percentHigh = Utils.calculatePercentageDifference(bitdaSummary.high, binanceSummary.high);
                const percentLow = Utils.calculatePercentageDifference(bitdaSummary.low, binanceSummary.low);
                const percentClose = Utils.calculatePercentageDifference(bitdaSummary.close, binanceSummary.close);
                const percentVolume = Utils.calculatePercentageDifference(bitdaSummary.volume, binanceSummary.volume);

                this.elements.percentOpen.textContent = Utils.formatPercentage(percentOpen);
                this.elements.percentHigh.textContent = Utils.formatPercentage(percentHigh);
                this.elements.percentLow.textContent = Utils.formatPercentage(percentLow);
                this.elements.percentClose.textContent = Utils.formatPercentage(percentClose);
                this.elements.percentVolume.textContent = Utils.formatPercentage(percentVolume);
            } else {
                // 如果只有一个平台有数据，清空差异行
                ['diffOpen', 'diffHigh', 'diffLow', 'diffClose', 'diffVolume',
                 'percentOpen', 'percentHigh', 'percentLow', 'percentClose', 'percentVolume'].forEach(id => {
                    this.elements[id].textContent = '-';
                });
            }

        } catch (error) {
            console.error('更新表格失败:', error);
        }
    }

    /**
     * 计算汇总数据
     * @param {Array} data - 数据数组
     * @returns {Object} 汇总数据
     */
    calculateSummaryData(data) {
        if (!Array.isArray(data) || data.length === 0) {
            return { open: null, high: null, low: null, close: null, volume: null };
        }

        // 使用第一个数据点的开盘价和最后一个数据点的收盘价
        const firstData = data[0];
        const lastData = data[data.length - 1];
        
        // 计算期间最高价和最低价
        const highs = data.map(item => item.high);
        const lows = data.map(item => item.low);
        const volumes = data.map(item => item.volume);

        return {
            open: firstData.open,
            high: Math.max(...highs),
            low: Math.min(...lows),
            close: lastData.close,
            volume: volumes.reduce((sum, vol) => sum + vol, 0)
        };
    }

    /**
     * 更新统计信息
     * @param {Object} data - 数据对象
     * @param {string} startTime - 开始时间
     * @param {string} endTime - 结束时间
     */
    updateStatistics(data, startTime, endTime) {
        try {
            // 计算数据点数量
            const bitdaCount = data.bitda.success ? data.bitda.data.length : 0;
            const binanceCount = data.binance.success ? data.binance.data.length : 0;
            this.elements.dataPointsCount.textContent = `Bitda: ${bitdaCount}, Binance: ${binanceCount}`;

            // 显示时间范围
            const startFormatted = Utils.formatTimestamp(new Date(startTime).getTime());
            const endFormatted = Utils.formatTimestamp(new Date(endTime).getTime());
            this.elements.timeRange.textContent = `${startFormatted} - ${endFormatted}`;

            // 计算平均价差和最大价差
            if (data.bitda.success && data.binance.success && data.bitda.data.length > 0 && data.binance.data.length > 0) {
                const aligned = Utils.alignDataByTimestamp(data.bitda.data, data.binance.data);
                
                if (aligned.aligned1.length > 0) {
                    const priceDiffs = aligned.aligned1.map((item, index) => {
                        return Math.abs(item.close - aligned.aligned2[index].close);
                    });

                    const avgDiff = priceDiffs.reduce((sum, diff) => sum + diff, 0) / priceDiffs.length;
                    const maxDiff = Math.max(...priceDiffs);

                    this.elements.avgPriceDiff.textContent = Utils.formatNumber(avgDiff);
                    this.elements.maxPriceDiff.textContent = Utils.formatNumber(maxDiff);
                } else {
                    this.elements.avgPriceDiff.textContent = '-';
                    this.elements.maxPriceDiff.textContent = '-';
                }
            } else {
                this.elements.avgPriceDiff.textContent = '-';
                this.elements.maxPriceDiff.textContent = '-';
            }

        } catch (error) {
            console.error('更新统计信息失败:', error);
        }
    }

    /**
     * 设置查询状态
     * @param {boolean} isQuerying - 是否正在查询
     */
    setQueryingState(isQuerying) {
        this.isQuerying = isQuerying;
        
        if (this.elements.queryBtn) {
            this.elements.queryBtn.disabled = isQuerying;
            const btnText = this.elements.queryBtn.querySelector('.btn-text');
            const btnLoading = this.elements.queryBtn.querySelector('.btn-loading');
            
            if (btnText && btnLoading) {
                btnText.style.display = isQuerying ? 'none' : 'inline';
                btnLoading.style.display = isQuerying ? 'inline' : 'none';
            }
        }
        
        if (this.elements.loadingIndicator) {
            this.elements.loadingIndicator.style.display = isQuerying ? 'block' : 'none';
        }
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showError(message) {
        if (this.elements.errorMessage && this.elements.errorText) {
            this.elements.errorText.textContent = message;
            this.elements.errorMessage.style.display = 'block';
        }
        this.hideResults();
        this.hideNoData();
    }

    /**
     * 隐藏错误信息
     */
    hideError() {
        if (this.elements.errorMessage) {
            this.elements.errorMessage.style.display = 'none';
        }
    }

    /**
     * 处理错误关闭按钮点击
     */
    handleCloseError() {
        this.hideError();
    }

    /**
     * 显示无数据信息
     */
    showNoData() {
        if (this.elements.noDataMessage) {
            this.elements.noDataMessage.style.display = 'block';
        }
        this.hideResults();
        this.hideError();
    }

    /**
     * 隐藏无数据信息
     */
    hideNoData() {
        if (this.elements.noDataMessage) {
            this.elements.noDataMessage.style.display = 'none';
        }
    }

    /**
     * 显示结果
     */
    showResults() {
        if (this.elements.resultsSection) {
            this.elements.resultsSection.style.display = 'block';
        }
        this.hideError();
        this.hideNoData();
    }

    /**
     * 隐藏结果
     */
    hideResults() {
        if (this.elements.resultsSection) {
            this.elements.resultsSection.style.display = 'none';
        }
    }

    /**
     * 隐藏所有消息
     */
    hideAllMessages() {
        this.hideError();
        this.hideNoData();
        this.hideResults();
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    window.app = new CryptoComparisonApp();
    window.app.init();
});
