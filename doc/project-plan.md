# 加密货币永续合约数据对比查询网页 - 项目规划

## 项目概述

本项目旨在开发一个Web应用，用于对比查询Bitda和Binance两个交易所的加密货币永续合约数据。用户可以选择时间范围和合约类型，查看两个平台的K线数据对比和详细的数据差异分析。

## 功能需求详细说明

### 1. 时间选择器
- **功能描述**：提供开始时间和结束时间两个日期时间选择器
- **精度要求**：支持精确到秒的时间选择
- **示例时间范围**：2025-07-29 00:00:00 至 2025-07-30 00:00:00
- **技术实现**：使用HTML5 datetime-local input或第三方日期选择器组件

### 2. 合约选择器
- **功能描述**：下拉选择框，包含24个期货合约选项
- **合约列表**：
  ```
  BTCUSDT, ETHUSDT, SOLUSDT, 1000PEPEUSDT, 1000SHIBUSDT,
  ADAUSDT, AVAXUSDT, BCHUSDT, DOGEUSDT, DOTUSDT,
  ETCUSDT, LTCUSDT, MELANIAUSDT, OPUSDT, SANDUSDT,
  SUIUSDT, TONUSDT, TRUMPUSDT, UNIUSDT, XLMUSDT, 
  XRPUSDT, CRVUSDT, XTZUSDT, DYDXUSDT
  ```
- **技术实现**：HTML select元素或自定义下拉组件

### 3. 数据展示
#### 3.1 K线图对比
- **功能描述**：同时展示Bitda和Binance的OHLC数据
- **图表类型**：K线图（蜡烛图）
- **对比方式**：双轴或叠加显示
- **技术实现**：Chart.js或ECharts图表库

#### 3.2 数据对比表格
- **表格内容**：
  - Bitda数据行：开盘价、最高价、最低价、收盘价
  - Binance数据行：开盘价、最高价、最低价、收盘价
  - 差异值行：Bitda - Binance
  - 差异百分比行：(Bitda - Binance) / Binance * 100%
- **数据格式**：保留适当小数位数，百分比显示

## 数据源API规格

### 1. Bitda API
- **URL模板**：`https://api.bitda.com/open/api/v2/market/kline?market={symbol}&type=1min`
- **请求方法**：GET
- **响应格式**：
  ```json
  {
    "code": 0,
    "msg": "success", 
    "data": [
      {
        "open": "1571.23",
        "high": "1573.89", 
        "low": "1571.23",
        "close": "1573.89",
        "volume": "3.02",
        "time": 1697620709569
      }
    ]
  }
  ```

### 2. Binance API
- **URL模板**：`https://fapi.binance.com/fapi/v1/klines?symbol={symbol}&interval=1m&limit=1500`
- **请求方法**：GET
- **响应格式**：数组格式，每个元素为：
  ```
  [
    开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量, 
    收盘时间, 成交额, 成交笔数, 主动买入成交量, 主动买入成交额, 忽略字段
  ]
  ```

## 技术架构

### 前端技术栈
- **HTML5**：页面结构和语义化标签
- **CSS3**：样式设计和响应式布局
- **JavaScript (ES6+)**：业务逻辑和API交互
- **图表库**：Chart.js 或 ECharts
- **日期选择器**：原生HTML5或第三方组件

### 数据处理
- **数据获取**：Fetch API进行异步请求
- **数据缓存**：浏览器内存缓存，无需本地存储
- **错误处理**：网络错误、API错误、数据格式错误处理
- **数据转换**：统一两个API的数据格式

### 响应式设计
- **桌面端**：>=1024px，多列布局
- **平板端**：768px-1023px，适配布局
- **移动端**：<768px，单列布局

## UI/UX设计要求

### 视觉设计
- **色彩方案**：现代化配色，主色调为蓝色系
- **字体**：无衬线字体，确保可读性
- **图标**：统一的图标风格
- **间距**：合理的留白和间距

### 用户体验
- **加载状态**：查询时显示加载动画
- **错误提示**：友好的错误信息展示
- **数据为空**：无数据时的提示信息
- **操作反馈**：按钮点击反馈效果

## 项目文件结构

```
futures/
├── doc/                    # 项目文档
│   ├── project-plan.md     # 项目规划文档
│   └── task-list.md        # 详细任务列表
├── index.html              # 主页面
├── css/
│   ├── style.css           # 主样式文件
│   └── responsive.css      # 响应式样式
├── js/
│   ├── main.js             # 主要业务逻辑
│   ├── api.js              # API数据获取
│   ├── chart.js            # 图表相关功能
│   └── utils.js            # 工具函数
└── assets/
    └── images/             # 图片资源
```

## 开发里程碑

1. **第一阶段**：项目结构和基础页面（1-2天）
2. **第二阶段**：API集成和数据处理（2-3天）
3. **第三阶段**：图表和表格功能（2-3天）
4. **第四阶段**：样式优化和响应式设计（1-2天）
5. **第五阶段**：测试和优化（1天）

## 验收标准

- [ ] 时间选择器功能正常，支持精确到秒
- [ ] 合约选择器包含所有24个合约选项
- [ ] 成功获取并展示Bitda和Binance数据
- [ ] K线图正确显示两个平台的对比数据
- [ ] 数据对比表格计算准确
- [ ] 响应式设计在各种设备上正常显示
- [ ] 错误处理和用户提示完善
- [ ] 页面加载性能良好
